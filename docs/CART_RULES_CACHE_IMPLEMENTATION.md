# Cart Rules Decision Cache Implementation

## Overview

This document describes the implementation of caching for Input Rule Decision Properties and Output Rule Decision Properties in the get-cart-rules-recommendations API. The caching mechanism improves performance by storing frequently accessed rule decisions and reducing the need to execute Drool rules repeatedly.

## Architecture

### Cache Key Structure

The cache uses composite keys to distinguish between different types of rules:

- **Input Rules**: `{ruleId}_input_rule`
- **Output Rules**: `{ruleId}_output_rule`

Where `ruleId` is the unique identifier for each rule.

### Components

#### 1. RuleDecisionCacheKey
- **Location**: `src/main/java/com/stpl/tech/kettle/domain/kettle/model/RuleDecisionCacheKey.java`
- **Purpose**: Composite key class for creating cache keys
- **Key Methods**:
  - `forInputRule(Integer ruleId)`: Creates key for input rules
  - `forOutputRule(Integer ruleId)`: Creates key for output rules
  - `toCacheKey()`: Generates string representation

#### 2. CartRulesDecisionCache Interface
- **Location**: `src/main/java/com/stpl/tech/kettle/crm/cache/CartRulesDecisionCache.java`
- **Purpose**: Defines caching operations for rule decisions
- **Key Methods**:
  - `getInputRuleDecision(Integer ruleId)`
  - `putInputRuleDecision(Integer ruleId, InputRulesDroolDecisionProperties inputRule)`
  - `getOutputRuleDecision(Integer ruleId)`
  - `putOutputRuleDecision(Integer ruleId, OutputRulesDroolDecisionProperties outputRule)`
  - Bulk operations for multiple rules
  - Cache clearing operations

#### 3. CartRulesDecisionCacheImpl
- **Location**: `src/main/java/com/stpl/tech/kettle/crm/cache/impl/CartRulesDecisionCacheImpl.java`
- **Purpose**: Hazelcast-based implementation of the cache interface
- **Features**:
  - Uses existing Hazelcast infrastructure
  - Separate maps for input and output rules
  - Null-safe operations
  - Comprehensive logging

#### 4. Updated CartRulesRecommendationServiceImpl
- **Location**: `src/main/java/com/stpl/tech/kettle/crm/service/impl/CartRulesRecommendationServiceImpl.java`
- **Changes**:
  - Integrated cache checking before Drool execution
  - Added helper methods for cache operations
  - Fallback mechanism for cache failures

## Cache Flow

### Input Rules Caching Flow

1. **Extract Rule IDs**: Parse `inputRulesToBeRun` from `CartRulesDroolDecisionProperties`
2. **Check Cache**: Query cache for existing input rules
3. **Identify Missing**: Determine which rules are not in cache
4. **Execute Drools**: Run Drool rules only for missing rules
5. **Cache Results**: Store newly executed rules in cache
6. **Return Combined**: Return both cached and newly executed rules

### Output Rules Caching Flow

1. **Extract Rule IDs**: Parse `outputRulesToBeRun` from each `InputRulesDroolDecisionProperties`
2. **Check Cache**: Query cache for existing output rules
3. **Identify Missing**: Determine which rules are not in cache
4. **Execute Drools**: Run Drool rules only for missing rules
5. **Cache Results**: Store newly executed rules in cache
6. **Return Combined**: Return both cached and newly executed rules

## Configuration

### Cache Constants
- **Location**: `src/main/java/com/stpl/tech/kettle/crm/util/CacheConstants.java`
- **New Constants**:
  - `INPUT_RULES_DECISION_CACHE = "MasterDataCache:inputRulesDecisionCache"`
  - `OUTPUT_RULES_DECISION_CACHE = "MasterDataCache:outputRulesDecisionCache"`

### Hazelcast Integration
- Uses existing `MasterHazelCastInstance`
- Follows established caching patterns in the project
- Automatic cache initialization via `@PostConstruct`

## API Endpoints

### Cache Management Endpoints
- **Base Path**: `/api/v1/cache-refresh`

#### Clear Input Rules Cache
- **Endpoint**: `POST /refresh-input-rules-cache`
- **Description**: Clears all cached input rule decisions

#### Clear Output Rules Cache
- **Endpoint**: `POST /refresh-output-rules-cache`
- **Description**: Clears all cached output rule decisions

#### Clear All Rules Cache
- **Endpoint**: `POST /refresh-all-rules-cache`
- **Description**: Clears both input and output rule caches

## Performance Benefits

1. **Reduced Drool Execution**: Avoids re-executing rules that are already cached
2. **Faster Response Times**: Cache lookups are significantly faster than Drool rule execution
3. **Scalability**: Reduces load on the Drool engine during high traffic
4. **Memory Efficiency**: Only caches rules that are actually used

## Error Handling

1. **Cache Failures**: Automatic fallback to direct Drool execution
2. **Null Safety**: All methods handle null inputs gracefully
3. **Exception Logging**: Comprehensive error logging for debugging
4. **Graceful Degradation**: System continues to work even if cache is unavailable

## Testing

### Unit Tests
- **Location**: `src/test/java/com/stpl/tech/kettle/crm/cache/impl/CartRulesDecisionCacheImplTest.java`
- **Coverage**: All cache operations, edge cases, null handling

### Integration Tests
- **Location**: `src/test/java/com/stpl/tech/kettle/crm/service/impl/CartRulesRecommendationServiceImplCacheTest.java`
- **Coverage**: Service integration, cache interaction, key extraction

## Monitoring and Maintenance

### Logging
- Cache hits/misses are logged at DEBUG level
- Cache operations are logged at INFO level
- Errors are logged at ERROR level with full stack traces

### Cache Metrics
- Number of cached vs newly executed rules
- Cache hit ratios
- Execution time comparisons

### Maintenance Operations
- Regular cache clearing via API endpoints
- Monitoring cache size and memory usage
- Performance monitoring of cache vs direct execution

## Future Enhancements

1. **TTL (Time To Live)**: Add expiration times for cached rules
2. **Cache Warming**: Pre-populate cache with frequently used rules
3. **Metrics Dashboard**: Real-time cache performance monitoring
4. **Distributed Caching**: Enhanced distribution across multiple nodes
5. **Cache Versioning**: Handle rule updates and versioning

## Usage Example

```java
// The cache is automatically used in the service
CartRulesRecommendationResponse response = cartRulesRecommendationService
    .getCartBasedRecommendations(request);

// Manual cache operations (if needed)
cartRulesDecisionCache.clearAllRulesCache();
```

## Dependencies

- Hazelcast (existing)
- Spring Framework (existing)
- Lombok (existing)
- JUnit 5 (for testing)
- Mockito (for testing)
