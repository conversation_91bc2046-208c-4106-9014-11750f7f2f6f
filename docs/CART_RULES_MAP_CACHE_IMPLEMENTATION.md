# Cart Rules Decision Map-Based Cache Implementation

## Overview

This document describes the implementation of a Map-based cache for Input Rule Decision Properties and Output Rule Decision Properties in the get-cart-rules-recommendations API. The caching mechanism improves performance by storing frequently accessed rule decisions in a ConcurrentHashMap and reducing the need to execute Drool rules repeatedly.

## Architecture

### Cache Key Structure

The cache uses composite keys to distinguish between different types of rules:

- **Input Rules**: `{ruleId}_input_rule`
- **Output Rules**: `{ruleId}_output_rule`

Where `ruleId` is the unique identifier for each rule.

### Cache Implementation

The cache is implemented as a simple `Map<String, Object>` using `ConcurrentHashMap` for thread safety, directly within the `CartRulesRecommendationServiceImpl` class.

```java
// Cache for input and output rule decision properties
// Key format: {ruleId}_{input_rule|output_rule}
private final Map<String, Object> ruleDecisionCache = new ConcurrentHashMap<>();
```

## Key Components

### 1. Cache Declaration
- **Location**: `CartRulesRecommendationServiceImpl.java`
- **Type**: `Map<String, Object>` using `ConcurrentHashMap`
- **Thread Safety**: Yes, using `ConcurrentHashMap`

### 2. Cache Key Creation
```java
private String createCacheKey(Integer ruleId, String code) {
    return ruleId + "_" + code;
}
```

### 3. Rule ID Extraction Methods
- `extractInputRuleIds()`: Parses input rule IDs from cart rules decision
- `extractOutputRuleIds()`: Parses output rule IDs from input rules

### 4. Cache-Enabled Execution Methods
- `getInputRulesWithCache()`: Checks cache before executing input rules
- `getOutputRulesWithCache()`: Checks cache before executing output rules

## Cache Flow

### Input Rules Caching Flow

1. **Extract Rule IDs**: Parse `inputRulesToBeRun` from `CartRulesDroolDecisionProperties`
2. **Check Cache**: Query cache using keys like `{ruleId}_input_rule`
3. **Identify Missing**: Determine which rules are not in cache
4. **Execute Drools**: Run Drool rules only for missing rules
5. **Cache Results**: Store newly executed rules with appropriate keys
6. **Return Combined**: Return both cached and newly executed rules

### Output Rules Caching Flow

1. **Extract Rule IDs**: Parse `outputRulesToBeRun` from each `InputRulesDroolDecisionProperties`
2. **Check Cache**: Query cache using keys like `{ruleId}_output_rule`
3. **Execute Drools**: Run Drool rules (caching results for future use)
4. **Cache Results**: Store executed rules with appropriate keys
5. **Return Results**: Return executed rules

## API Methods

### Cache Access Methods
```java
// Get cached rules by ID
public InputRulesDroolDecisionProperties getCachedInputRule(Integer ruleId)
public OutputRulesDroolDecisionProperties getCachedOutputRule(Integer ruleId)
```

### Cache Management Methods
```java
// Clear cache operations
public void clearRuleDecisionCache()           // Clear all cached rules
public void clearInputRulesCache()            // Clear input rules only
public void clearOutputRulesCache()           // Clear output rules only

// Cache statistics
public Map<String, Object> getCacheStatistics()
```

## REST API Endpoints

### Cache Management Endpoints
- **Base Path**: `/api/v1/cache-refresh`

#### Clear Input Rules Cache
- **Endpoint**: `POST /refresh-input-rules-cache`
- **Description**: Clears all cached input rule decisions

#### Clear Output Rules Cache
- **Endpoint**: `POST /refresh-output-rules-cache`
- **Description**: Clears all cached output rule decisions

#### Clear All Rules Cache
- **Endpoint**: `POST /refresh-all-rules-cache`
- **Description**: Clears both input and output rule caches

#### Get Cache Statistics
- **Endpoint**: `POST /get-cache-statistics`
- **Description**: Returns cache statistics including entry counts
- **Response**:
```json
{
  "totalCacheEntries": 150,
  "inputRuleEntries": 75,
  "outputRuleEntries": 75
}
```

## Performance Benefits

1. **Reduced Drool Execution**: Avoids re-executing rules that are already cached
2. **Faster Response Times**: Map lookups are significantly faster than Drool rule execution
3. **Memory Efficient**: Only caches rules that are actually used
4. **Thread Safe**: ConcurrentHashMap ensures thread safety without blocking

## Error Handling

1. **Cache Failures**: Automatic fallback to direct Drool execution
2. **Invalid Rule IDs**: Gracefully handles non-numeric rule IDs
3. **Null Safety**: All methods handle null inputs gracefully
4. **Exception Logging**: Comprehensive error logging for debugging

## Usage Examples

### Automatic Caching (Transparent)
```java
// Cache is automatically used when calling the main API
CartRulesRecommendationResponse response = cartRulesRecommendationService
    .getCartBasedRecommendations(request);
```

### Manual Cache Operations
```java
// Check if a rule is cached
InputRulesDroolDecisionProperties cachedRule = 
    cartRulesRecommendationService.getCachedInputRule(123);

// Clear specific cache types
cartRulesRecommendationService.clearInputRulesCache();
cartRulesRecommendationService.clearOutputRulesCache();

// Get cache statistics
Map<String, Object> stats = cartRulesRecommendationService.getCacheStatistics();
```

## Testing

### Unit Tests
- **Location**: `CartRulesRecommendationServiceImplMapCacheTest.java`
- **Coverage**: 
  - Cache key generation
  - Rule ID extraction
  - Cache operations
  - Error handling
  - Statistics generation

### Test Scenarios
- Valid rule ID extraction
- Invalid rule ID handling
- Empty/null input handling
- Cache key format validation
- Cache statistics accuracy

## Monitoring and Maintenance

### Logging
- Cache hits/misses logged at DEBUG level
- Cache operations logged at INFO level
- Errors logged at ERROR level with stack traces

### Cache Statistics
- Total cache entries
- Input rule entries count
- Output rule entries count
- Available via REST API and service method

### Maintenance Operations
- Cache clearing via REST endpoints
- Statistics monitoring
- Memory usage tracking

## Advantages of Map-Based Approach

1. **Simplicity**: No external dependencies, uses standard Java collections
2. **Performance**: Direct memory access, no network overhead
3. **Control**: Full control over cache behavior and lifecycle
4. **Debugging**: Easy to inspect cache contents during development
5. **Memory Efficiency**: Automatic garbage collection of unused entries

## Limitations

1. **Single JVM**: Cache is not shared across multiple application instances
2. **Memory Bound**: Limited by available heap memory
3. **No Persistence**: Cache is lost on application restart
4. **No TTL**: No automatic expiration of cached entries

## Future Enhancements

1. **TTL Support**: Add time-based expiration for cached entries
2. **Size Limits**: Implement LRU eviction when cache grows too large
3. **Metrics Integration**: Add detailed performance metrics
4. **Cache Warming**: Pre-populate cache with frequently used rules
5. **Persistence**: Optional persistence to survive application restarts

## Configuration

No additional configuration required. The cache is automatically initialized when the service starts.

## Dependencies

- Java ConcurrentHashMap (built-in)
- Spring Framework (existing)
- Lombok (existing)
- JUnit 5 (for testing)
- Mockito (for testing)
