# Cart Rules Decision Spring Cache Implementation

## Overview

This document describes the implementation of Spring Cache (`@Cacheable` and `@CacheEvict`) for Input Rule Decision Properties and Output Rule Decision Properties in the get-cart-rules-recommendations API. The caching mechanism uses Spring's declarative caching annotations for a clean, standardized approach to caching.

## Architecture

### Cache Configuration

The cache is configured using Spring's `@EnableCaching` annotation and `ConcurrentMapCacheManager` for in-memory caching.

### Cache Names

- **Input Rules Cache**: `inputRulesCache`
- **Output Rules Cache**: `outputRulesCache`

### Cache Key Structure

- **Input Rules**: `{inputRulesToBeRun}_{droolVersion}`
- **Output Rules**: `{ruleNum}_{outputRulesToBeRun}_{droolVersion}`

## Key Components

### 1. Cache Configuration (`CacheConfig.java`)

```java
@Configuration
@EnableCaching
public class CacheConfig {
    
    @Bean
    public CacheManager cacheManager() {
        ConcurrentMapCacheManager cacheManager = new ConcurrentMapCacheManager();
        cacheManager.setCacheNames(Arrays.asList(
            "inputRulesCache",
            "outputRulesCache"
        ));
        return cacheManager;
    }
}
```

### 2. Cacheable Methods

#### Input Rules Caching
```java
@Cacheable(value = "inputRulesCache", 
           key = "#cartRulesDecision.inputRulesToBeRun + '_' + #inputRulesDroolVersion")
public List<InputRulesDroolDecisionProperties> executeInputRulesFromCartResult(
    CartRulesDroolDecisionProperties cartRulesDecision, 
    String inputRulesDroolVersion)
```

#### Output Rules Caching
```java
@Cacheable(value = "outputRulesCache", 
           key = "#inputRule.ruleNum + '_' + #inputRule.outputRulesToBeRun + '_' + #outputRulesDroolVersion")
public List<OutputRulesDroolDecisionProperties> executeOutputRulesFromCartResult(
    InputRulesDroolDecisionProperties inputRule, 
    String outputRulesDroolVersion)
```

### 3. Cache Eviction Methods

#### Clear All Input Rules
```java
@CacheEvict(value = "inputRulesCache", allEntries = true)
public void clearInputRulesCache()
```

#### Clear All Output Rules
```java
@CacheEvict(value = "outputRulesCache", allEntries = true)
public void clearOutputRulesCache()
```

#### Clear All Rules
```java
@CacheEvict(value = {"inputRulesCache", "outputRulesCache"}, allEntries = true)
public void clearAllRulesCache()
```

#### Evict Specific Entries
```java
@CacheEvict(value = "inputRulesCache", 
            key = "#cartRulesDecision.inputRulesToBeRun + '_' + #inputRulesDroolVersion")
public void evictInputRuleFromCache(CartRulesDroolDecisionProperties cartRulesDecision, 
                                   String inputRulesDroolVersion)

@CacheEvict(value = "outputRulesCache", 
            key = "#inputRule.ruleNum + '_' + #inputRule.outputRulesToBeRun + '_' + #outputRulesDroolVersion")
public void evictOutputRuleFromCache(InputRulesDroolDecisionProperties inputRule, 
                                    String outputRulesDroolVersion)
```

## Cache Flow

### Input Rules Caching Flow

1. **Method Call**: `executeInputRulesFromCartResult()` is called
2. **Cache Check**: Spring checks if result exists in `inputRulesCache` with generated key
3. **Cache Hit**: If found, returns cached result without executing method
4. **Cache Miss**: If not found, executes method and caches the result
5. **Return**: Returns either cached or newly computed result

### Output Rules Caching Flow

1. **Method Call**: `executeOutputRulesFromCartResult()` is called for each input rule
2. **Cache Check**: Spring checks if result exists in `outputRulesCache` with generated key
3. **Cache Hit**: If found, returns cached result without executing method
4. **Cache Miss**: If not found, executes method and caches the result
5. **Return**: Returns either cached or newly computed result

## Cache Key Examples

### Input Rules Cache Keys
```
"1_2_3_v1.0"           // inputRulesToBeRun=1_2_3, droolVersion=v1.0
"5_10_15_v2.0"         // inputRulesToBeRun=5_10_15, droolVersion=v2.0
"100_v1.5"             // inputRulesToBeRun=100, droolVersion=v1.5
```

### Output Rules Cache Keys
```
"1_10_20_v1.0"         // ruleNum=1, outputRulesToBeRun=10_20, droolVersion=v1.0
"5_50_100_v2.0"        // ruleNum=5, outputRulesToBeRun=50_100, droolVersion=v2.0
"10_200_v1.5"          // ruleNum=10, outputRulesToBeRun=200, droolVersion=v1.5
```

## REST API Endpoints

### Cache Management Endpoints
- **Base Path**: `/api/v1/cache-refresh`

#### Clear Input Rules Cache
- **Endpoint**: `POST /refresh-input-rules-cache`
- **Description**: Clears all cached input rule decisions
- **Response**: `true` if successful, `false` otherwise

#### Clear Output Rules Cache
- **Endpoint**: `POST /refresh-output-rules-cache`
- **Description**: Clears all cached output rule decisions
- **Response**: `true` if successful, `false` otherwise

#### Clear All Rules Cache
- **Endpoint**: `POST /refresh-all-rules-cache`
- **Description**: Clears both input and output rule caches
- **Response**: `true` if successful, `false` otherwise

#### Get Cache Statistics
- **Endpoint**: `POST /get-cache-statistics`
- **Description**: Returns cache configuration information
- **Response**:
```json
{
  "inputRulesCacheName": "inputRulesCache",
  "outputRulesCacheName": "outputRulesCache",
  "cacheProvider": "Spring Cache",
  "note": "Detailed statistics depend on cache provider configuration"
}
```

## Advantages of Spring Cache Approach

1. **Declarative**: Clean separation of caching concerns from business logic
2. **Standardized**: Uses Spring's well-established caching framework
3. **Flexible**: Easy to change cache providers (Redis, Ehcache, etc.)
4. **AOP-Based**: Automatic proxy creation for cache operations
5. **Key Generation**: Automatic cache key generation using SpEL expressions
6. **Thread Safe**: Built-in thread safety with ConcurrentMapCacheManager

## Configuration Options

### Cache Manager Types

#### In-Memory (Default)
```java
@Bean
public CacheManager cacheManager() {
    return new ConcurrentMapCacheManager("inputRulesCache", "outputRulesCache");
}
```

#### Redis (Optional)
```java
@Bean
public CacheManager cacheManager(RedisConnectionFactory connectionFactory) {
    RedisCacheManager.Builder builder = RedisCacheManager
        .RedisCacheManagerBuilder
        .fromConnectionFactory(connectionFactory)
        .cacheDefaults(cacheConfiguration());
    return builder.build();
}
```

#### Ehcache (Optional)
```java
@Bean
public CacheManager cacheManager() {
    return new EhCacheCacheManager(ehCacheManagerFactory().getObject());
}
```

## Performance Benefits

1. **Automatic Caching**: No manual cache management required
2. **Method-Level Caching**: Caches entire method results
3. **Key-Based Lookup**: Fast cache lookups using generated keys
4. **Conditional Caching**: Can add conditions for when to cache
5. **TTL Support**: Can configure time-to-live for cache entries (provider dependent)

## Error Handling

1. **Cache Failures**: Graceful degradation - method executes normally if cache fails
2. **Key Generation**: Robust key generation using SpEL expressions
3. **Null Values**: Configurable null value handling
4. **Exception Handling**: Cache operations don't interfere with business logic

## Testing

### Unit Tests
- **Location**: `CartRulesRecommendationServiceImplSpringCacheTest.java`
- **Coverage**: 
  - Cache method execution
  - Cache eviction operations
  - Cache statistics
  - Error scenarios

### Integration Tests
- Test cache behavior with actual Spring context
- Verify cache hits and misses
- Test cache eviction functionality

## Monitoring and Maintenance

### Logging
- Cache hits/misses can be logged at DEBUG level
- Cache eviction operations logged at INFO level
- Method execution logged when cache misses occur

### Cache Statistics
- Basic cache configuration information available via REST API
- Detailed statistics depend on cache provider
- Can integrate with monitoring tools (Micrometer, etc.)

### Maintenance Operations
- Cache clearing via REST endpoints
- Specific cache entry eviction methods
- Cache configuration updates via Spring profiles

## Usage Examples

### Automatic Caching (Transparent)
```java
// Cache is automatically used when calling these methods
List<InputRulesDroolDecisionProperties> inputRules = 
    cartRulesRecommendationService.executeInputRulesFromCartResult(cartRulesDecision, version);

List<OutputRulesDroolDecisionProperties> outputRules = 
    cartRulesRecommendationService.executeOutputRulesFromCartResult(inputRule, version);
```

### Manual Cache Management
```java
// Clear specific cache types
cartRulesRecommendationService.clearInputRulesCache();
cartRulesRecommendationService.clearOutputRulesCache();
cartRulesRecommendationService.clearAllRulesCache();

// Evict specific entries
cartRulesRecommendationService.evictInputRuleFromCache(cartRulesDecision, version);
cartRulesRecommendationService.evictOutputRuleFromCache(inputRule, version);
```

## Future Enhancements

1. **Redis Integration**: Distributed caching across multiple instances
2. **Cache Metrics**: Integration with Micrometer for detailed metrics
3. **Conditional Caching**: Add conditions for when to cache based on business rules
4. **TTL Configuration**: Configure time-to-live for different cache entries
5. **Cache Warming**: Pre-populate caches with frequently used data
6. **Custom Key Generators**: More sophisticated cache key generation strategies

## Dependencies

- Spring Framework (existing)
- Spring Cache (spring-context)
- ConcurrentMapCacheManager (built-in)
- Lombok (existing)
- JUnit 5 (for testing)
- Mockito (for testing)
