package examples;

import com.stpl.tech.kettle.crm.service.impl.CartRulesRecommendationServiceImpl;
import com.stpl.tech.kettle.domain.kettle.model.InputRulesDroolDecisionProperties;
import com.stpl.tech.kettle.domain.kettle.model.OutputRulesDroolDecisionProperties;
import com.stpl.tech.kettle.domain.kettle.model.request.CartRulesRecommendationRequest;
import com.stpl.tech.kettle.domain.kettle.model.response.CartRulesRecommendationResponse;

import java.util.Map;

/**
 * Example demonstrating the Cart Rules Cache functionality
 * This shows how the cache works transparently and how to manage it manually
 */
public class CartRulesCacheExample {

    private CartRulesRecommendationServiceImpl cartRulesService;

    public void demonstrateCacheUsage() {
        
        // 1. AUTOMATIC CACHING (Transparent Usage)
        System.out.println("=== Automatic Caching Demo ===");
        
        // First call - rules will be executed and cached
        CartRulesRecommendationRequest request = CartRulesRecommendationRequest.builder()
                .customerId(12345L)
                .unitId(100)
                .custType("EXISTING")
                .build();
        
        long startTime = System.currentTimeMillis();
        CartRulesRecommendationResponse response1 = cartRulesService.getCartBasedRecommendations(request);
        long firstCallTime = System.currentTimeMillis() - startTime;
        
        System.out.println("First call took: " + firstCallTime + "ms");
        System.out.println("Input rules found: " + response1.getInputRulesDroolDecisionProperties().size());
        System.out.println("Output rules found: " + response1.getOutputRules().size());
        
        // Second call - same request, should use cached rules
        startTime = System.currentTimeMillis();
        CartRulesRecommendationResponse response2 = cartRulesService.getCartBasedRecommendations(request);
        long secondCallTime = System.currentTimeMillis() - startTime;
        
        System.out.println("Second call took: " + secondCallTime + "ms");
        System.out.println("Performance improvement: " + (firstCallTime - secondCallTime) + "ms");
        
        // 2. MANUAL CACHE OPERATIONS
        System.out.println("\n=== Manual Cache Operations Demo ===");
        
        // Check cache statistics
        Map<String, Object> stats = cartRulesService.getCacheStatistics();
        System.out.println("Cache Statistics:");
        System.out.println("  Total entries: " + stats.get("totalCacheEntries"));
        System.out.println("  Input rule entries: " + stats.get("inputRuleEntries"));
        System.out.println("  Output rule entries: " + stats.get("outputRuleEntries"));
        
        // 3. INDIVIDUAL RULE LOOKUP
        System.out.println("\n=== Individual Rule Lookup Demo ===");
        
        // Try to get a specific cached input rule
        InputRulesDroolDecisionProperties cachedInputRule = cartRulesService.getCachedInputRule(1);
        if (cachedInputRule != null) {
            System.out.println("Found cached input rule 1:");
            System.out.println("  Rule Number: " + cachedInputRule.getRuleNum());
            System.out.println("  Cart State: " + cachedInputRule.getCurrentCartState());
            System.out.println("  Output Rules: " + cachedInputRule.getOutputRulesToBeRun());
        } else {
            System.out.println("Input rule 1 not found in cache");
        }
        
        // Try to get a specific cached output rule
        OutputRulesDroolDecisionProperties cachedOutputRule = cartRulesService.getCachedOutputRule(10);
        if (cachedOutputRule != null) {
            System.out.println("Found cached output rule 10:");
            System.out.println("  Rule Number: " + cachedOutputRule.getRulesNumber());
            System.out.println("  Type: " + cachedOutputRule.getType());
            System.out.println("  Recommendation Type: " + cachedOutputRule.getRecommendationType());
            System.out.println("  Product Count: " + cachedOutputRule.getProductCount());
        } else {
            System.out.println("Output rule 10 not found in cache");
        }
        
        // 4. CACHE MANAGEMENT
        System.out.println("\n=== Cache Management Demo ===");
        
        // Clear specific cache types
        System.out.println("Clearing input rules cache...");
        cartRulesService.clearInputRulesCache();
        
        // Check statistics after clearing input rules
        stats = cartRulesService.getCacheStatistics();
        System.out.println("After clearing input rules:");
        System.out.println("  Total entries: " + stats.get("totalCacheEntries"));
        System.out.println("  Input rule entries: " + stats.get("inputRuleEntries"));
        System.out.println("  Output rule entries: " + stats.get("outputRuleEntries"));
        
        // Clear all cache
        System.out.println("Clearing all cache...");
        cartRulesService.clearRuleDecisionCache();
        
        // Final statistics
        stats = cartRulesService.getCacheStatistics();
        System.out.println("After clearing all cache:");
        System.out.println("  Total entries: " + stats.get("totalCacheEntries"));
        System.out.println("  Input rule entries: " + stats.get("inputRuleEntries"));
        System.out.println("  Output rule entries: " + stats.get("outputRuleEntries"));
    }
    
    /**
     * Demonstrates cache key format
     */
    public void demonstrateCacheKeyFormat() {
        System.out.println("\n=== Cache Key Format Demo ===");
        
        // Input rule cache keys
        System.out.println("Input Rule Cache Keys:");
        System.out.println("  Rule ID 1: 1_input_rule");
        System.out.println("  Rule ID 25: 25_input_rule");
        System.out.println("  Rule ID 100: 100_input_rule");
        
        // Output rule cache keys
        System.out.println("Output Rule Cache Keys:");
        System.out.println("  Rule ID 10: 10_output_rule");
        System.out.println("  Rule ID 50: 50_output_rule");
        System.out.println("  Rule ID 200: 200_output_rule");
    }
    
    /**
     * Demonstrates performance comparison
     */
    public void demonstratePerformanceComparison() {
        System.out.println("\n=== Performance Comparison Demo ===");
        
        CartRulesRecommendationRequest request = CartRulesRecommendationRequest.builder()
                .customerId(12345L)
                .unitId(100)
                .custType("EXISTING")
                .build();
        
        // Clear cache to start fresh
        cartRulesService.clearRuleDecisionCache();
        
        // Measure multiple calls
        int numberOfCalls = 5;
        long[] callTimes = new long[numberOfCalls];
        
        for (int i = 0; i < numberOfCalls; i++) {
            long startTime = System.currentTimeMillis();
            cartRulesService.getCartBasedRecommendations(request);
            callTimes[i] = System.currentTimeMillis() - startTime;
            
            System.out.println("Call " + (i + 1) + " took: " + callTimes[i] + "ms");
        }
        
        // Calculate performance improvement
        long firstCallTime = callTimes[0];
        long averageSubsequentCalls = 0;
        for (int i = 1; i < numberOfCalls; i++) {
            averageSubsequentCalls += callTimes[i];
        }
        averageSubsequentCalls = averageSubsequentCalls / (numberOfCalls - 1);
        
        System.out.println("\nPerformance Summary:");
        System.out.println("  First call (no cache): " + firstCallTime + "ms");
        System.out.println("  Average cached calls: " + averageSubsequentCalls + "ms");
        System.out.println("  Performance improvement: " + (firstCallTime - averageSubsequentCalls) + "ms");
        System.out.println("  Speed increase: " + String.format("%.1f", (double) firstCallTime / averageSubsequentCalls) + "x");
    }
    
    /**
     * Main method to run all demonstrations
     */
    public static void main(String[] args) {
        CartRulesCacheExample example = new CartRulesCacheExample();
        
        // Note: In a real application, cartRulesService would be injected
        // example.cartRulesService = ...; 
        
        System.out.println("Cart Rules Cache Demonstration");
        System.out.println("==============================");
        
        example.demonstrateCacheKeyFormat();
        // example.demonstrateCacheUsage();
        // example.demonstratePerformanceComparison();
        
        System.out.println("\nDemo completed!");
    }
}
