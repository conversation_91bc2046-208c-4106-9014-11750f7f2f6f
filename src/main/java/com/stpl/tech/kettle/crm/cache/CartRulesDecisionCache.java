package com.stpl.tech.kettle.crm.cache;

import com.stpl.tech.kettle.domain.kettle.model.InputRulesDroolDecisionProperties;
import com.stpl.tech.kettle.domain.kettle.model.OutputRulesDroolDecisionProperties;
import com.stpl.tech.kettle.domain.kettle.model.RuleDecisionCacheKey;

import java.util.List;

/**
 * Cache interface for cart rules decision properties
 * Provides caching functionality for input and output rule decision properties
 */
public interface CartRulesDecisionCache {

    /**
     * Get input rule decision properties from cache
     * @param ruleId the rule identifier
     * @return InputRulesDroolDecisionProperties if found in cache, null otherwise
     */
    InputRulesDroolDecisionProperties getInputRuleDecision(Integer ruleId);

    /**
     * Store input rule decision properties in cache
     * @param ruleId the rule identifier
     * @param inputRule the input rule decision properties to cache
     */
    void putInputRuleDecision(Integer ruleId, InputRulesDroolDecisionProperties inputRule);

    /**
     * Get output rule decision properties from cache
     * @param ruleId the rule identifier
     * @return OutputRulesDroolDecisionProperties if found in cache, null otherwise
     */
    OutputRulesDroolDecisionProperties getOutputRuleDecision(Integer ruleId);

    /**
     * Store output rule decision properties in cache
     * @param ruleId the rule identifier
     * @param outputRule the output rule decision properties to cache
     */
    void putOutputRuleDecision(Integer ruleId, OutputRulesDroolDecisionProperties outputRule);

    /**
     * Get multiple input rule decisions from cache
     * @param ruleIds list of rule identifiers
     * @return list of input rule decision properties found in cache
     */
    List<InputRulesDroolDecisionProperties> getInputRuleDecisions(List<Integer> ruleIds);

    /**
     * Store multiple input rule decisions in cache
     * @param inputRules list of input rule decision properties to cache
     */
    void putInputRuleDecisions(List<InputRulesDroolDecisionProperties> inputRules);

    /**
     * Get multiple output rule decisions from cache
     * @param ruleIds list of rule identifiers
     * @return list of output rule decision properties found in cache
     */
    List<OutputRulesDroolDecisionProperties> getOutputRuleDecisions(List<Integer> ruleIds);

    /**
     * Store multiple output rule decisions in cache
     * @param outputRules list of output rule decision properties to cache
     */
    void putOutputRuleDecisions(List<OutputRulesDroolDecisionProperties> outputRules);

    /**
     * Clear input rules cache
     */
    void clearInputRulesCache();

    /**
     * Clear output rules cache
     */
    void clearOutputRulesCache();

    /**
     * Clear all rules cache
     */
    void clearAllRulesCache();
}
