package com.stpl.tech.kettle.crm.cache.impl;

import com.hazelcast.core.HazelcastInstance;
import com.stpl.tech.kettle.crm.cache.CartRulesDecisionCache;
import com.stpl.tech.kettle.crm.util.CacheConstants;
import com.stpl.tech.kettle.domain.kettle.model.InputRulesDroolDecisionProperties;
import com.stpl.tech.kettle.domain.kettle.model.OutputRulesDroolDecisionProperties;
import com.stpl.tech.kettle.domain.kettle.model.RuleDecisionCacheKey;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Implementation of CartRulesDecisionCache using Hazelcast
 * Provides caching functionality for input and output rule decision properties
 */
@Service
@Log4j2
public class CartRulesDecisionCacheImpl implements CartRulesDecisionCache {

    @Autowired
    @Qualifier(value = "MasterHazelCastInstance")
    private HazelcastInstance instance;

    private Map<String, InputRulesDroolDecisionProperties> inputRulesCache;
    private Map<String, OutputRulesDroolDecisionProperties> outputRulesCache;

    @PostConstruct
    public void createCache() {
        log.info("POST-CONSTRUCT CartRulesDecisionCache - STARTED");
        long time = System.currentTimeMillis();
        
        inputRulesCache = instance.getMap(CacheConstants.INPUT_RULES_DECISION_CACHE);
        outputRulesCache = instance.getMap(CacheConstants.OUTPUT_RULES_DECISION_CACHE);
        
        log.info("POST-CONSTRUCT CartRulesDecisionCache - took {} ms", System.currentTimeMillis() - time);
    }

    @Override
    public InputRulesDroolDecisionProperties getInputRuleDecision(Integer ruleId) {
        if (Objects.isNull(ruleId)) {
            return null;
        }
        
        String cacheKey = RuleDecisionCacheKey.forInputRule(ruleId).toCacheKey();
        InputRulesDroolDecisionProperties result = inputRulesCache.get(cacheKey);
        
        if (Objects.nonNull(result)) {
            log.debug("Input rule decision found in cache for rule ID: {}", ruleId);
        } else {
            log.debug("Input rule decision not found in cache for rule ID: {}", ruleId);
        }
        
        return result;
    }

    @Override
    public void putInputRuleDecision(Integer ruleId, InputRulesDroolDecisionProperties inputRule) {
        if (Objects.isNull(ruleId) || Objects.isNull(inputRule)) {
            log.warn("Cannot cache input rule decision - ruleId or inputRule is null");
            return;
        }
        
        String cacheKey = RuleDecisionCacheKey.forInputRule(ruleId).toCacheKey();
        inputRulesCache.put(cacheKey, inputRule);
        
        log.debug("Cached input rule decision for rule ID: {}", ruleId);
    }

    @Override
    public OutputRulesDroolDecisionProperties getOutputRuleDecision(Integer ruleId) {
        if (Objects.isNull(ruleId)) {
            return null;
        }
        
        String cacheKey = RuleDecisionCacheKey.forOutputRule(ruleId).toCacheKey();
        OutputRulesDroolDecisionProperties result = outputRulesCache.get(cacheKey);
        
        if (Objects.nonNull(result)) {
            log.debug("Output rule decision found in cache for rule ID: {}", ruleId);
        } else {
            log.debug("Output rule decision not found in cache for rule ID: {}", ruleId);
        }
        
        return result;
    }

    @Override
    public void putOutputRuleDecision(Integer ruleId, OutputRulesDroolDecisionProperties outputRule) {
        if (Objects.isNull(ruleId) || Objects.isNull(outputRule)) {
            log.warn("Cannot cache output rule decision - ruleId or outputRule is null");
            return;
        }
        
        String cacheKey = RuleDecisionCacheKey.forOutputRule(ruleId).toCacheKey();
        outputRulesCache.put(cacheKey, outputRule);
        
        log.debug("Cached output rule decision for rule ID: {}", ruleId);
    }

    @Override
    public List<InputRulesDroolDecisionProperties> getInputRuleDecisions(List<Integer> ruleIds) {
        List<InputRulesDroolDecisionProperties> results = new ArrayList<>();
        
        if (Objects.isNull(ruleIds) || ruleIds.isEmpty()) {
            return results;
        }
        
        for (Integer ruleId : ruleIds) {
            InputRulesDroolDecisionProperties inputRule = getInputRuleDecision(ruleId);
            if (Objects.nonNull(inputRule)) {
                results.add(inputRule);
            }
        }
        
        log.debug("Retrieved {} input rule decisions from cache out of {} requested", results.size(), ruleIds.size());
        return results;
    }

    @Override
    public void putInputRuleDecisions(List<InputRulesDroolDecisionProperties> inputRules) {
        if (Objects.isNull(inputRules) || inputRules.isEmpty()) {
            return;
        }
        
        for (InputRulesDroolDecisionProperties inputRule : inputRules) {
            if (Objects.nonNull(inputRule) && Objects.nonNull(inputRule.getRuleNum())) {
                putInputRuleDecision(inputRule.getRuleNum(), inputRule);
            }
        }
        
        log.debug("Cached {} input rule decisions", inputRules.size());
    }

    @Override
    public List<OutputRulesDroolDecisionProperties> getOutputRuleDecisions(List<Integer> ruleIds) {
        List<OutputRulesDroolDecisionProperties> results = new ArrayList<>();
        
        if (Objects.isNull(ruleIds) || ruleIds.isEmpty()) {
            return results;
        }
        
        for (Integer ruleId : ruleIds) {
            OutputRulesDroolDecisionProperties outputRule = getOutputRuleDecision(ruleId);
            if (Objects.nonNull(outputRule)) {
                results.add(outputRule);
            }
        }
        
        log.debug("Retrieved {} output rule decisions from cache out of {} requested", results.size(), ruleIds.size());
        return results;
    }

    @Override
    public void putOutputRuleDecisions(List<OutputRulesDroolDecisionProperties> outputRules) {
        if (Objects.isNull(outputRules) || outputRules.isEmpty()) {
            return;
        }
        
        for (OutputRulesDroolDecisionProperties outputRule : outputRules) {
            if (Objects.nonNull(outputRule) && Objects.nonNull(outputRule.getRulesNumber())) {
                putOutputRuleDecision(outputRule.getRulesNumber(), outputRule);
            }
        }
        
        log.debug("Cached {} output rule decisions", outputRules.size());
    }

    @Override
    public void clearInputRulesCache() {
        inputRulesCache.clear();
        log.info("Cleared input rules cache");
    }

    @Override
    public void clearOutputRulesCache() {
        outputRulesCache.clear();
        log.info("Cleared output rules cache");
    }

    @Override
    public void clearAllRulesCache() {
        clearInputRulesCache();
        clearOutputRulesCache();
        log.info("Cleared all rules cache");
    }
}
