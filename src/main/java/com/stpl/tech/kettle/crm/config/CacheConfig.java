package com.stpl.tech.kettle.crm.config;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Cache configuration for Cart Rules Decision caching
 * Enables Spring Cache with in-memory ConcurrentMapCacheManager
 */
@Configuration
@EnableCaching
public class CacheConfig {

    /**
     * Configure cache manager for cart rules decision caching
     * Uses ConcurrentMapCacheManager for in-memory caching
     * 
     * @return CacheManager instance
     */
    @Bean
    public CacheManager cacheManager() {
        ConcurrentMapCacheManager cacheManager = new ConcurrentMapCacheManager();
        
        // Pre-configure cache names for input and output rules
        cacheManager.setCacheNames(java.util.Arrays.asList(
            "inputRulesCache",
            "outputRulesCache"
        ));
        
        // Allow dynamic cache creation for additional caches if needed
        cacheManager.setAllowNullValues(false);
        
        return cacheManager;
    }
}
