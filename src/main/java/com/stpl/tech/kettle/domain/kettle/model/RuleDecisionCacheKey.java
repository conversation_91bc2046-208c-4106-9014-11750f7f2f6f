package com.stpl.tech.kettle.domain.kettle.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Objects;

/**
 * Cache key class for rule decision properties
 * Used to create composite keys for caching input and output rule decisions
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RuleDecisionCacheKey implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Rule identifier - the rule number/ID
     */
    private Integer ruleId;

    /**
     * Code type to distinguish between input and output rules
     * Values: "input_rule", "output_rule"
     */
    private String code;

    /**
     * Creates a cache key for input rule
     */
    public static RuleDecisionCacheKey forInputRule(Integer ruleId) {
        return RuleDecisionCacheKey.builder()
                .ruleId(ruleId)
                .code("input_rule")
                .build();
    }

    /**
     * Creates a cache key for output rule
     */
    public static RuleDecisionCacheKey forOutputRule(Integer ruleId) {
        return RuleDecisionCacheKey.builder()
                .ruleId(ruleId)
                .code("output_rule")
                .build();
    }

    /**
     * Generates string representation for cache key
     */
    public String toCacheKey() {
        return ruleId + "_" + code;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        RuleDecisionCacheKey that = (RuleDecisionCacheKey) o;
        return Objects.equals(ruleId, that.ruleId) && Objects.equals(code, that.code);
    }

    @Override
    public int hashCode() {
        return Objects.hash(ruleId, code);
    }

    @Override
    public String toString() {
        return toCacheKey();
    }
}
