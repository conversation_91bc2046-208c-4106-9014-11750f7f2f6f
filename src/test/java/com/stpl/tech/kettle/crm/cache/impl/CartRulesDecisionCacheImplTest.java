package com.stpl.tech.kettle.crm.cache.impl;

import com.hazelcast.core.HazelcastInstance;
import com.stpl.tech.kettle.domain.kettle.model.InputRulesDroolDecisionProperties;
import com.stpl.tech.kettle.domain.kettle.model.OutputRulesDroolDecisionProperties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CartRulesDecisionCacheImplTest {

    @Mock
    private HazelcastInstance hazelcastInstance;

    @Mock
    private Map<String, InputRulesDroolDecisionProperties> inputRulesCache;

    @Mock
    private Map<String, OutputRulesDroolDecisionProperties> outputRulesCache;

    @InjectMocks
    private CartRulesDecisionCacheImpl cartRulesDecisionCache;

    @BeforeEach
    void setUp() {
        when(hazelcastInstance.getMap(anyString())).thenReturn(inputRulesCache, outputRulesCache);
        cartRulesDecisionCache.createCache();
    }

    @Test
    void testGetInputRuleDecision_Found() {
        // Given
        Integer ruleId = 1;
        InputRulesDroolDecisionProperties expectedRule = InputRulesDroolDecisionProperties.builder()
                .ruleNum(ruleId)
                .currentCartState("ACTIVE")
                .build();
        
        when(inputRulesCache.get("1_input_rule")).thenReturn(expectedRule);

        // When
        InputRulesDroolDecisionProperties result = cartRulesDecisionCache.getInputRuleDecision(ruleId);

        // Then
        assertNotNull(result);
        assertEquals(ruleId, result.getRuleNum());
        assertEquals("ACTIVE", result.getCurrentCartState());
        verify(inputRulesCache).get("1_input_rule");
    }

    @Test
    void testGetInputRuleDecision_NotFound() {
        // Given
        Integer ruleId = 1;
        when(inputRulesCache.get("1_input_rule")).thenReturn(null);

        // When
        InputRulesDroolDecisionProperties result = cartRulesDecisionCache.getInputRuleDecision(ruleId);

        // Then
        assertNull(result);
        verify(inputRulesCache).get("1_input_rule");
    }

    @Test
    void testPutInputRuleDecision() {
        // Given
        Integer ruleId = 1;
        InputRulesDroolDecisionProperties inputRule = InputRulesDroolDecisionProperties.builder()
                .ruleNum(ruleId)
                .currentCartState("ACTIVE")
                .build();

        // When
        cartRulesDecisionCache.putInputRuleDecision(ruleId, inputRule);

        // Then
        verify(inputRulesCache).put("1_input_rule", inputRule);
    }

    @Test
    void testGetOutputRuleDecision_Found() {
        // Given
        Integer ruleId = 1;
        OutputRulesDroolDecisionProperties expectedRule = OutputRulesDroolDecisionProperties.builder()
                .rulesNumber(ruleId)
                .type("RECOMMENDATION")
                .build();
        
        when(outputRulesCache.get("1_output_rule")).thenReturn(expectedRule);

        // When
        OutputRulesDroolDecisionProperties result = cartRulesDecisionCache.getOutputRuleDecision(ruleId);

        // Then
        assertNotNull(result);
        assertEquals(ruleId, result.getRulesNumber());
        assertEquals("RECOMMENDATION", result.getType());
        verify(outputRulesCache).get("1_output_rule");
    }

    @Test
    void testPutOutputRuleDecision() {
        // Given
        Integer ruleId = 1;
        OutputRulesDroolDecisionProperties outputRule = OutputRulesDroolDecisionProperties.builder()
                .rulesNumber(ruleId)
                .type("RECOMMENDATION")
                .build();

        // When
        cartRulesDecisionCache.putOutputRuleDecision(ruleId, outputRule);

        // Then
        verify(outputRulesCache).put("1_output_rule", outputRule);
    }

    @Test
    void testGetInputRuleDecisions_MultipleRules() {
        // Given
        List<Integer> ruleIds = Arrays.asList(1, 2, 3);
        InputRulesDroolDecisionProperties rule1 = InputRulesDroolDecisionProperties.builder()
                .ruleNum(1)
                .currentCartState("ACTIVE")
                .build();
        InputRulesDroolDecisionProperties rule2 = InputRulesDroolDecisionProperties.builder()
                .ruleNum(2)
                .currentCartState("PROCESSING")
                .build();
        
        when(inputRulesCache.get("1_input_rule")).thenReturn(rule1);
        when(inputRulesCache.get("2_input_rule")).thenReturn(rule2);
        when(inputRulesCache.get("3_input_rule")).thenReturn(null);

        // When
        List<InputRulesDroolDecisionProperties> results = cartRulesDecisionCache.getInputRuleDecisions(ruleIds);

        // Then
        assertEquals(2, results.size());
        assertTrue(results.contains(rule1));
        assertTrue(results.contains(rule2));
        verify(inputRulesCache).get("1_input_rule");
        verify(inputRulesCache).get("2_input_rule");
        verify(inputRulesCache).get("3_input_rule");
    }

    @Test
    void testPutInputRuleDecisions() {
        // Given
        List<InputRulesDroolDecisionProperties> inputRules = Arrays.asList(
                InputRulesDroolDecisionProperties.builder().ruleNum(1).currentCartState("ACTIVE").build(),
                InputRulesDroolDecisionProperties.builder().ruleNum(2).currentCartState("PROCESSING").build()
        );

        // When
        cartRulesDecisionCache.putInputRuleDecisions(inputRules);

        // Then
        verify(inputRulesCache).put("1_input_rule", inputRules.get(0));
        verify(inputRulesCache).put("2_input_rule", inputRules.get(1));
    }

    @Test
    void testClearInputRulesCache() {
        // When
        cartRulesDecisionCache.clearInputRulesCache();

        // Then
        verify(inputRulesCache).clear();
    }

    @Test
    void testClearOutputRulesCache() {
        // When
        cartRulesDecisionCache.clearOutputRulesCache();

        // Then
        verify(outputRulesCache).clear();
    }

    @Test
    void testClearAllRulesCache() {
        // When
        cartRulesDecisionCache.clearAllRulesCache();

        // Then
        verify(inputRulesCache).clear();
        verify(outputRulesCache).clear();
    }

    @Test
    void testGetInputRuleDecision_NullRuleId() {
        // When
        InputRulesDroolDecisionProperties result = cartRulesDecisionCache.getInputRuleDecision(null);

        // Then
        assertNull(result);
        verify(inputRulesCache, never()).get(anyString());
    }

    @Test
    void testPutInputRuleDecision_NullValues() {
        // When
        cartRulesDecisionCache.putInputRuleDecision(null, null);
        cartRulesDecisionCache.putInputRuleDecision(1, null);

        // Then
        verify(inputRulesCache, never()).put(anyString(), any());
    }
}
