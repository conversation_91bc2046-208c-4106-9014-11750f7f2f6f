package com.stpl.tech.kettle.crm.service.impl;

import com.stpl.tech.kettle.crm.cache.CartRulesDecisionCache;
import com.stpl.tech.kettle.domain.kettle.model.CartRulesDroolDecisionProperties;
import com.stpl.tech.kettle.domain.kettle.model.InputRulesDroolDecisionProperties;
import com.stpl.tech.kettle.domain.kettle.model.OutputRulesDroolDecisionProperties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CartRulesRecommendationServiceImplCacheTest {

    @Mock
    private CartRulesDecisionCache cartRulesDecisionCache;

    @Mock
    private CartRulesOrchestrationService cartRulesOrchestrationService;

    @InjectMocks
    private CartRulesRecommendationServiceImpl cartRulesRecommendationService;

    private Method extractInputRuleIdsMethod;
    private Method extractOutputRuleIdsMethod;

    @BeforeEach
    void setUp() throws Exception {
        // Access private methods for testing
        extractInputRuleIdsMethod = CartRulesRecommendationServiceImpl.class
                .getDeclaredMethod("extractInputRuleIds", CartRulesDroolDecisionProperties.class);
        extractInputRuleIdsMethod.setAccessible(true);

        extractOutputRuleIdsMethod = CartRulesRecommendationServiceImpl.class
                .getDeclaredMethod("extractOutputRuleIds", InputRulesDroolDecisionProperties.class);
        extractOutputRuleIdsMethod.setAccessible(true);
    }

    @Test
    void testExtractInputRuleIds() throws Exception {
        // Given
        CartRulesDroolDecisionProperties cartRulesDecision = CartRulesDroolDecisionProperties.builder()
                .inputRulesToBeRun("1_2_3")
                .build();

        // When
        @SuppressWarnings("unchecked")
        List<Integer> result = (List<Integer>) extractInputRuleIdsMethod.invoke(cartRulesRecommendationService, cartRulesDecision);

        // Then
        assertEquals(3, result.size());
        assertTrue(result.contains(1));
        assertTrue(result.contains(2));
        assertTrue(result.contains(3));
    }

    @Test
    void testExtractInputRuleIds_EmptyString() throws Exception {
        // Given
        CartRulesDroolDecisionProperties cartRulesDecision = CartRulesDroolDecisionProperties.builder()
                .inputRulesToBeRun("")
                .build();

        // When
        @SuppressWarnings("unchecked")
        List<Integer> result = (List<Integer>) extractInputRuleIdsMethod.invoke(cartRulesRecommendationService, cartRulesDecision);

        // Then
        assertEquals(1, result.size()); // Empty string creates one empty element
    }

    @Test
    void testExtractInputRuleIds_NullInput() throws Exception {
        // Given
        CartRulesDroolDecisionProperties cartRulesDecision = CartRulesDroolDecisionProperties.builder()
                .inputRulesToBeRun(null)
                .build();

        // When
        @SuppressWarnings("unchecked")
        List<Integer> result = (List<Integer>) extractInputRuleIdsMethod.invoke(cartRulesRecommendationService, cartRulesDecision);

        // Then
        assertEquals(0, result.size());
    }

    @Test
    void testExtractOutputRuleIds() throws Exception {
        // Given
        InputRulesDroolDecisionProperties inputRule = InputRulesDroolDecisionProperties.builder()
                .outputRulesToBeRun("10_20_30")
                .build();

        // When
        @SuppressWarnings("unchecked")
        List<Integer> result = (List<Integer>) extractOutputRuleIdsMethod.invoke(cartRulesRecommendationService, inputRule);

        // Then
        assertEquals(3, result.size());
        assertTrue(result.contains(10));
        assertTrue(result.contains(20));
        assertTrue(result.contains(30));
    }

    @Test
    void testExtractOutputRuleIds_NullInput() throws Exception {
        // Given
        InputRulesDroolDecisionProperties inputRule = InputRulesDroolDecisionProperties.builder()
                .outputRulesToBeRun(null)
                .build();

        // When
        @SuppressWarnings("unchecked")
        List<Integer> result = (List<Integer>) extractOutputRuleIdsMethod.invoke(cartRulesRecommendationService, inputRule);

        // Then
        assertEquals(0, result.size());
    }

    @Test
    void testCacheKeyGeneration() {
        // Test that cache keys are generated correctly
        // This is implicitly tested through the cache implementation
        // but we can verify the key format matches our expectations
        
        // Input rule cache key format: {ruleId}_input_rule
        // Output rule cache key format: {ruleId}_output_rule
        
        // Given
        Integer ruleId = 123;
        InputRulesDroolDecisionProperties inputRule = InputRulesDroolDecisionProperties.builder()
                .ruleNum(ruleId)
                .currentCartState("ACTIVE")
                .build();
        
        OutputRulesDroolDecisionProperties outputRule = OutputRulesDroolDecisionProperties.builder()
                .rulesNumber(ruleId)
                .type("RECOMMENDATION")
                .build();

        // When
        cartRulesDecisionCache.putInputRuleDecision(ruleId, inputRule);
        cartRulesDecisionCache.putOutputRuleDecision(ruleId, outputRule);

        // Then
        verify(cartRulesDecisionCache).putInputRuleDecision(ruleId, inputRule);
        verify(cartRulesDecisionCache).putOutputRuleDecision(ruleId, outputRule);
    }

    @Test
    void testCacheInteraction_InputRules() {
        // Given
        List<Integer> ruleIds = Arrays.asList(1, 2, 3);
        List<InputRulesDroolDecisionProperties> cachedRules = Arrays.asList(
                InputRulesDroolDecisionProperties.builder().ruleNum(1).currentCartState("ACTIVE").build(),
                InputRulesDroolDecisionProperties.builder().ruleNum(2).currentCartState("PROCESSING").build()
        );
        
        when(cartRulesDecisionCache.getInputRuleDecisions(ruleIds)).thenReturn(cachedRules);

        // When
        List<InputRulesDroolDecisionProperties> result = cartRulesDecisionCache.getInputRuleDecisions(ruleIds);

        // Then
        assertEquals(2, result.size());
        verify(cartRulesDecisionCache).getInputRuleDecisions(ruleIds);
    }

    @Test
    void testCacheInteraction_OutputRules() {
        // Given
        List<Integer> ruleIds = Arrays.asList(10, 20, 30);
        List<OutputRulesDroolDecisionProperties> cachedRules = Arrays.asList(
                OutputRulesDroolDecisionProperties.builder().rulesNumber(10).type("RECOMMENDATION").build(),
                OutputRulesDroolDecisionProperties.builder().rulesNumber(20).type("DISCOUNT").build()
        );
        
        when(cartRulesDecisionCache.getOutputRuleDecisions(ruleIds)).thenReturn(cachedRules);

        // When
        List<OutputRulesDroolDecisionProperties> result = cartRulesDecisionCache.getOutputRuleDecisions(ruleIds);

        // Then
        assertEquals(2, result.size());
        verify(cartRulesDecisionCache).getOutputRuleDecisions(ruleIds);
    }

    @Test
    void testCacheClearOperations() {
        // When
        cartRulesDecisionCache.clearInputRulesCache();
        cartRulesDecisionCache.clearOutputRulesCache();
        cartRulesDecisionCache.clearAllRulesCache();

        // Then
        verify(cartRulesDecisionCache).clearInputRulesCache();
        verify(cartRulesDecisionCache).clearOutputRulesCache();
        verify(cartRulesDecisionCache).clearAllRulesCache();
    }
}
