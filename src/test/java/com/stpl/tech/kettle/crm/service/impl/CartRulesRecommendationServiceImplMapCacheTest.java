package com.stpl.tech.kettle.crm.service.impl;

import com.stpl.tech.kettle.domain.kettle.model.CartRulesDroolDecisionProperties;
import com.stpl.tech.kettle.domain.kettle.model.InputRulesDroolDecisionProperties;
import com.stpl.tech.kettle.domain.kettle.model.OutputRulesDroolDecisionProperties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CartRulesRecommendationServiceImplMapCacheTest {

    @Mock
    private CartRulesOrchestrationService cartRulesOrchestrationService;

    @InjectMocks
    private CartRulesRecommendationServiceImpl cartRulesRecommendationService;

    private Method createCacheKeyMethod;
    private Method extractInputRuleIdsMethod;
    private Method extractOutputRuleIdsMethod;

    @BeforeEach
    void setUp() throws Exception {
        // Access private methods for testing
        createCacheKeyMethod = CartRulesRecommendationServiceImpl.class
                .getDeclaredMethod("createCacheKey", Integer.class, String.class);
        createCacheKeyMethod.setAccessible(true);

        extractInputRuleIdsMethod = CartRulesRecommendationServiceImpl.class
                .getDeclaredMethod("extractInputRuleIds", CartRulesDroolDecisionProperties.class);
        extractInputRuleIdsMethod.setAccessible(true);

        extractOutputRuleIdsMethod = CartRulesRecommendationServiceImpl.class
                .getDeclaredMethod("extractOutputRuleIds", InputRulesDroolDecisionProperties.class);
        extractOutputRuleIdsMethod.setAccessible(true);
    }

    @Test
    void testCreateCacheKey() throws Exception {
        // Given
        Integer ruleId = 123;
        String code = "input_rule";

        // When
        String result = (String) createCacheKeyMethod.invoke(cartRulesRecommendationService, ruleId, code);

        // Then
        assertEquals("123_input_rule", result);
    }

    @Test
    void testCreateCacheKey_OutputRule() throws Exception {
        // Given
        Integer ruleId = 456;
        String code = "output_rule";

        // When
        String result = (String) createCacheKeyMethod.invoke(cartRulesRecommendationService, ruleId, code);

        // Then
        assertEquals("456_output_rule", result);
    }

    @Test
    void testExtractInputRuleIds() throws Exception {
        // Given
        CartRulesDroolDecisionProperties cartRulesDecision = CartRulesDroolDecisionProperties.builder()
                .inputRulesToBeRun("1_2_3")
                .build();

        // When
        @SuppressWarnings("unchecked")
        List<Integer> result = (List<Integer>) extractInputRuleIdsMethod.invoke(cartRulesRecommendationService, cartRulesDecision);

        // Then
        assertEquals(3, result.size());
        assertTrue(result.contains(1));
        assertTrue(result.contains(2));
        assertTrue(result.contains(3));
    }

    @Test
    void testExtractInputRuleIds_WithEmptyStrings() throws Exception {
        // Given
        CartRulesDroolDecisionProperties cartRulesDecision = CartRulesDroolDecisionProperties.builder()
                .inputRulesToBeRun("1__3_")
                .build();

        // When
        @SuppressWarnings("unchecked")
        List<Integer> result = (List<Integer>) extractInputRuleIdsMethod.invoke(cartRulesRecommendationService, cartRulesDecision);

        // Then
        assertEquals(2, result.size()); // Empty strings should be filtered out
        assertTrue(result.contains(1));
        assertTrue(result.contains(3));
    }

    @Test
    void testExtractInputRuleIds_NullInput() throws Exception {
        // Given
        CartRulesDroolDecisionProperties cartRulesDecision = CartRulesDroolDecisionProperties.builder()
                .inputRulesToBeRun(null)
                .build();

        // When
        @SuppressWarnings("unchecked")
        List<Integer> result = (List<Integer>) extractInputRuleIdsMethod.invoke(cartRulesRecommendationService, cartRulesDecision);

        // Then
        assertEquals(0, result.size());
    }

    @Test
    void testExtractOutputRuleIds() throws Exception {
        // Given
        InputRulesDroolDecisionProperties inputRule = InputRulesDroolDecisionProperties.builder()
                .outputRulesToBeRun("10_20_30")
                .build();

        // When
        @SuppressWarnings("unchecked")
        List<Integer> result = (List<Integer>) extractOutputRuleIdsMethod.invoke(cartRulesRecommendationService, inputRule);

        // Then
        assertEquals(3, result.size());
        assertTrue(result.contains(10));
        assertTrue(result.contains(20));
        assertTrue(result.contains(30));
    }

    @Test
    void testGetCachedInputRule_NotFound() {
        // Given
        Integer ruleId = 1;

        // When
        InputRulesDroolDecisionProperties result = cartRulesRecommendationService.getCachedInputRule(ruleId);

        // Then
        assertNull(result);
    }

    @Test
    void testGetCachedOutputRule_NotFound() {
        // Given
        Integer ruleId = 1;

        // When
        OutputRulesDroolDecisionProperties result = cartRulesRecommendationService.getCachedOutputRule(ruleId);

        // Then
        assertNull(result);
    }

    @Test
    void testGetCachedInputRule_NullRuleId() {
        // When
        InputRulesDroolDecisionProperties result = cartRulesRecommendationService.getCachedInputRule(null);

        // Then
        assertNull(result);
    }

    @Test
    void testGetCachedOutputRule_NullRuleId() {
        // When
        OutputRulesDroolDecisionProperties result = cartRulesRecommendationService.getCachedOutputRule(null);

        // Then
        assertNull(result);
    }

    @Test
    void testClearRuleDecisionCache() {
        // When
        cartRulesRecommendationService.clearRuleDecisionCache();

        // Then - should not throw exception
        // Cache should be empty after clearing
        Map<String, Object> stats = cartRulesRecommendationService.getCacheStatistics();
        assertEquals(0, stats.get("totalCacheEntries"));
    }

    @Test
    void testClearInputRulesCache() {
        // When
        cartRulesRecommendationService.clearInputRulesCache();

        // Then - should not throw exception
        Map<String, Object> stats = cartRulesRecommendationService.getCacheStatistics();
        assertEquals(0L, stats.get("inputRuleEntries"));
    }

    @Test
    void testClearOutputRulesCache() {
        // When
        cartRulesRecommendationService.clearOutputRulesCache();

        // Then - should not throw exception
        Map<String, Object> stats = cartRulesRecommendationService.getCacheStatistics();
        assertEquals(0L, stats.get("outputRuleEntries"));
    }

    @Test
    void testGetCacheStatistics() {
        // When
        Map<String, Object> stats = cartRulesRecommendationService.getCacheStatistics();

        // Then
        assertNotNull(stats);
        assertTrue(stats.containsKey("totalCacheEntries"));
        assertTrue(stats.containsKey("inputRuleEntries"));
        assertTrue(stats.containsKey("outputRuleEntries"));
        assertEquals(0, stats.get("totalCacheEntries"));
        assertEquals(0L, stats.get("inputRuleEntries"));
        assertEquals(0L, stats.get("outputRuleEntries"));
    }

    @Test
    void testCacheKeyFormat() throws Exception {
        // Test that cache keys follow the expected format
        
        // Input rule key
        String inputKey = (String) createCacheKeyMethod.invoke(cartRulesRecommendationService, 123, "input_rule");
        assertEquals("123_input_rule", inputKey);
        
        // Output rule key
        String outputKey = (String) createCacheKeyMethod.invoke(cartRulesRecommendationService, 456, "output_rule");
        assertEquals("456_output_rule", outputKey);
    }

    @Test
    void testExtractRuleIds_InvalidFormat() throws Exception {
        // Given - input with invalid number format
        CartRulesDroolDecisionProperties cartRulesDecision = CartRulesDroolDecisionProperties.builder()
                .inputRulesToBeRun("1_abc_3")
                .build();

        // When
        @SuppressWarnings("unchecked")
        List<Integer> result = (List<Integer>) extractInputRuleIdsMethod.invoke(cartRulesRecommendationService, cartRulesDecision);

        // Then - should skip invalid numbers and continue with valid ones
        assertEquals(2, result.size());
        assertTrue(result.contains(1));
        assertTrue(result.contains(3));
    }
}
